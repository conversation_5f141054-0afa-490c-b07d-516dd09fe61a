import SwiftUI
import UIKit

struct QRCodeLoginView: View {
    @EnvironmentObject private var viewModel: QRCodeLoginViewModel

    var body: some View {
        VStack(spacing: 20) {
            Text(String(localized: viewModel.title))
                .font(.title2)
                .multilineTextAlignment(.center)
                .padding(.bottom, 50)
                .padding(.horizontal, 20)

            // Main content based on login status
            switch viewModel.loginStatus {
            case .idle:
                 InitialPlaceholderView()
                 
            case .fetchingQR:
                ProgressView()
                    .progressViewStyle(.circular)
                    .frame(width: 250, height: 250)
                    
            case .waitingForScan: // Includes QR code and timer
                QRCodeDisplayView(qrImage: viewModel.qrCodeImage, 
                                  remainingSeconds: viewModel.remainingSeconds,
                                  verificationNumber: viewModel.verificationNumber)
                   
            case .finalizingLogin:
                ProgressView()
                     .progressViewStyle(.circular)
                     .frame(width: 250, height: 250)
                    
            case .verifyingUserStatus:
                ProgressView()
                     .progressViewStyle(.circular)
                     .frame(width: 250, height: 250)
                    
            case .complete:
                CompletionView(message: "Login Successful!") // Simple success view
            
            case .expired:
                 InitialPlaceholderView()
                     .overlay {
                         Text("QR Code Expired")
                             .font(.headline)
                             .foregroundStyle(.red)
                     }

            case .error(let message):
                 InitialPlaceholderView()
                     .overlay {
                         Text(message)
                             .font(.headline)
                             .foregroundStyle(.red)
                             .multilineTextAlignment(.center)
                             .padding()
                     }
            }

            // Use computed property for refresh button visibility
            if showRefreshButton {
                Button(viewModel.loginStatus == .fetchingQR ? "Fetching..." : "Refresh QR Code") {
                    viewModel.fetchQRCode()
                }
                .disabled(viewModel.loginStatus == .fetchingQR) // Disable while fetching
            }

        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onAppear {
            // Fetch QR code only if idle
            if viewModel.loginStatus == .idle {
                 viewModel.fetchQRCode()
            }
        }
        .onDisappear {
             viewModel.cancelOperations()
         }
    }
    
    // Computed property to determine button visibility
    private var showRefreshButton: Bool {
        switch viewModel.loginStatus {
        case .expired: // Only show refresh button when expired
            return true
        default:
            return false
        }
    }
    
    // --- Subviews for different states --- 
    
    @ViewBuilder
    private func InitialPlaceholderView() -> some View {
         Rectangle()
             .fill(.gray.opacity(0.1))
             .frame(width: 250, height: 250)
             .overlay {
                 Text("QR Code will appear here")
                     .foregroundStyle(.secondary)
             }
    }
    
    @ViewBuilder
    private func QRCodeDisplayView(qrImage: UIImage?,
                                   remainingSeconds: Int?,
                                   verificationNumber: String?) -> some View {
        VStack(spacing: 15) {
#if os(tvOS)
            if let image = qrImage {
                Image(uiImage: image)
                    .resizable()
                    .interpolation(.none)
                    .scaledToFit()
                    .frame(width: 250, height: 250)
                    .border(Color.gray, width: 1)
                    .padding(.bottom, 30)
            } else {
                // Should ideally not happen in .waitingForScan state, but as fallback:
                Rectangle()
                    .fill(.red.opacity(0.1))
                    .frame(width: 250, height: 250)
                    .overlay { Text("QR Image Missing").foregroundStyle(.red) }
            }
#else
            if let actionURL = viewModel.actionURL,
               let actionTitle = viewModel.actionButtonTitle,
               let actionImage = viewModel.actionButtonImage {
                Button(String(localized: actionTitle), systemImage: actionImage) {
                    if let url = URL(string: actionURL) {
                        UIApplication.shared.open(url)
                    }
                }
                .bold()
                .foregroundStyle(.background)
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
                .padding(.bottom, 50)
            }
#endif
        }
        
        // Display Verification Number
        if let number = verificationNumber {
            VStack(spacing: 30) {
                Text("Select this number in Naver login:")
                    .font(.caption)
                    .foregroundStyle(.secondary)
                Text(number)
                    .font(.system(size: 60, weight: .bold))
                    .foregroundStyle(Color.primary)
                    .padding(20)
                    .border(Color.primary, width: 2)
            }
            .padding(.top, 10)
        } else {
            // Placeholder if number isn't loaded yet (should be rare in this state)
            Text("Verification Number: --")
                .font(.caption)
                .foregroundStyle(.secondary)
                .padding(.top, 10)
        }
    }
    
    @ViewBuilder
    private func CompletionView(message: String) -> some View {
         VStack {
             Image(systemName: "checkmark.circle.fill")
                 .resizable()
                 .scaledToFit()
                 .frame(width: 80, height: 80)
                 .foregroundStyle(.green)
             Text(message)
                 .font(.title3)
                 .padding(.top)
         }
         .frame(width: 250, height: 250) // Match size for consistency
    }
    
    // Helper to format seconds into MM:SS
    private func formatTime(seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
}

#Preview("test") {
    VStack(spacing: 15) {
        Button("Open Naver App", systemImage: "n.circle") {
            if let url = URL(string: "https://naver.com") {
                UIApplication.shared.open(url)
            }
        }
        .bold()
        .foregroundStyle(.background)
        .buttonStyle(.borderedProminent)
        .controlSize(.large)
    }
    .padding(50)
    .background(.red)
}

#Preview("Default View") {
    QRCodeLoginView()
        .environmentObject(QRCodeLoginViewModel())
}

// Preview for idle state
#Preview("Idle State") {
    let viewModel = QRCodeLoginViewModel()
    viewModel.loginStatus = .idle
    
    return QRCodeLoginView()
        .environmentObject(viewModel)
}

// Preview for fetchingQR state
#Preview("Fetching QR") {
    let viewModel = QRCodeLoginViewModel()
    viewModel.loginStatus = .fetchingQR
    
    return QRCodeLoginView()
        .environmentObject(viewModel)
}

// Preview for waitingForScan state with mock data
#Preview("Waiting For Scan") {
    let viewModel = QRCodeLoginViewModel()
    // Create a sample QR code image
    let sampleQRSize = CGSize(width: 250, height: 250)
    let renderer = UIGraphicsImageRenderer(size: sampleQRSize)
    let qrImage = renderer.image { ctx in
        UIColor.white.setFill()
        ctx.fill(CGRect(origin: .zero, size: sampleQRSize))
        
        // Draw some mock QR pattern
        UIColor.black.setFill()
        for _ in 0..<20 {
            let x = CGFloat.random(in: 20..<230)
            let y = CGFloat.random(in: 20..<230)
            let size = CGFloat.random(in: 5..<15)
            ctx.fill(CGRect(x: x, y: y, width: size, height: size))
        }
    }
    
    viewModel.qrCodeImage = qrImage
    // Timer is still needed for expiration logic but it's no longer displayed
    viewModel.remainingSeconds = 120 
    viewModel.verificationNumber = "123456"
    viewModel.loginStatus = .waitingForScan(sessionID: "mock-session", appsession: "mock-appsession")
    
    return QRCodeLoginView()
        .environmentObject(viewModel)
}

// Preview for finalizingLogin state
#Preview("Finalizing Login") {
    let viewModel = QRCodeLoginViewModel()
    viewModel.loginStatus = .finalizingLogin(sessionID: "mock-session", appsession: "mock-appsession")
    
    return QRCodeLoginView()
        .environmentObject(viewModel)
}

// Preview for verifyingUserStatus state
#Preview("Verifying User Status") {
    let viewModel = QRCodeLoginViewModel()
    viewModel.loginStatus = .verifyingUserStatus
    
    return QRCodeLoginView()
        .environmentObject(viewModel)
}

// Preview for complete state
#Preview("Complete") {
    let viewModel = QRCodeLoginViewModel()
    viewModel.loginStatus = .complete
    
    return QRCodeLoginView()
        .environmentObject(viewModel)
}

// Preview for expired state
#Preview("Expired") {
    let viewModel = QRCodeLoginViewModel()
    viewModel.errorMessage = "QR code has expired. Please try again."
    viewModel.loginStatus = .expired
    
    return QRCodeLoginView()
        .environmentObject(viewModel)
}

// Preview for error state
#Preview("Error") {
    let viewModel = QRCodeLoginViewModel()
    viewModel.loginStatus = .error("Network connection failed. Please check your internet connection.")
    
    return QRCodeLoginView()
        .environmentObject(viewModel)
} 
