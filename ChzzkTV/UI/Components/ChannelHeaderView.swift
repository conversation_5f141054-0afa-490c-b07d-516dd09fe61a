import SwiftUI

struct ChannelHeaderView: View {
    let channel: UIChannel
    let isFollowing: Bool
    let isHidden: Bool
    let onFollowToggle: () -> Void
    
    var body: some View {
        VStack {
            HStack(alignment: .top, spacing: Constants.channelInfoSpacing) {
                image
                
                VStack(alignment: .leading, spacing: Constants.channelInfoSpacing) {
                    VStack(alignment: .leading, spacing: 12) {
                        title
                        followerCount
#if os(tvOS)
                        description
#endif
                    }
#if os(tvOS)
                    HStack(spacing: 40) {
                        followButton
                    }
#endif
                }
                
                Spacer()
            }
            .padding(.vertical)
#if !os(tvOS)
            VStack(spacing: 30) {
                description
                followButton
            }
#endif
        }
    }
    
    @ViewBuilder
    var image: some View {
        if let imageUrl = channel.imageUrl {
            AsyncImage(url: imageUrl) { image in
                image
                    .resizable()
                    .scaledToFill()
            } placeholder: {
                Rectangle()
                    .foregroundColor(.gray.opacity(0.3))
            }
            .frame(width: Constants.channelInfoImageSize,
                   height: Constants.channelInfoImageSize)
            .clipShape(Circle())
        } else {
            Image(systemName: "person.circle.fill")
                .resizable()
                .foregroundColor(.gray)
                .frame(width: Constants.channelInfoImageSize,
                       height: Constants.channelInfoImageSize)
        }
    }
    
    @ViewBuilder
    var title: some View {
        Text(channel.name)
            .font(.title2)
            .fontWeight(.bold)
    }
    
    @ViewBuilder
    var followerCount: some View {
        Text(channel.followerCountFormatted)
            .font(.subheadline)
            .foregroundColor(.secondary)
    }
    
    @ViewBuilder
    var description: some View {
        if let description = channel.description {
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    @ViewBuilder
    var followButton: some View {
        Button(action: onFollowToggle) {
            Label(
                isFollowing ? String(localized: "Unfollow") : String(localized: "Follow"),
                systemImage: isFollowing ? "heart.fill" : "heart"
            )
#if os(tvOS)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
#else
            .padding()
            .frame(maxWidth: .infinity)
            .background(.chzzk)
            .foregroundStyle(.background)
            .clipShape(Capsule())
#endif
        }
    }
}

#Preview {
    ChannelHeaderView(
        channel: StreamPreview.sampleUIChannel,
        isFollowing: false,
        isHidden: false,
        onFollowToggle: {}
    )
    .padding()
} 
